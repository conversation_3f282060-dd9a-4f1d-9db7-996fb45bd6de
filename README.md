# Life Agent Demo

A LangGraph-based AI assistant for life management that provides helpful tools and conversational AI through a command-line interface.

## Features

- 🤖 **Conversational AI**: Chat with an intelligent agent powered by OpenAI's GPT models
- 🛠️ **Built-in Tools**: Access to time/date functions, calculations, and more
- 📊 **LangGraph Workflow**: Structured agent workflow with tool integration
- 💬 **CLI Interface**: Easy-to-use command-line interface with interactive chat mode
- ⚙️ **Configurable**: Customizable model settings and API configurations

## Setup

1. **Clone and navigate to the project**:
   ```bash
   cd /path/to/life-agent/demo
   ```

2. **Install dependencies**:
   ```bash
   uv sync
   ```

3. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

4. **Install the package**:
   ```bash
   uv pip install -e .
   ```

## Usage

### Command Line Interface

**Interactive chat mode**:
```bash
life-agent chat --interactive
# or
life-agent chat -i
```

**Single message**:
```bash
life-agent chat "What's the current time?"
```

**Show configuration**:
```bash
life-agent config
```

**List available tools**:
```bash
life-agent tools
```

### Python API

```python
from src.life_agent.core.agent import create_agent

# Create agent
agent = create_agent()

# Chat with the agent
response = agent.chat("What day of the week is 2024-12-25?")
print(response)
```

## Available Tools

- **get_current_time**: Get current date and time
- **calculate**: Perform basic mathematical calculations
- **get_day_of_week**: Get day of week for a specific date
- **days_between_dates**: Calculate days between two dates

## Configuration

The agent can be configured through environment variables in your `.env` file:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
DEFAULT_MODEL=gpt-4o-mini
TEMPERATURE=0.7
MAX_TOKENS=1000

# LangSmith (Optional)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=life-agent-demo
```

## Project Structure

```
src/life_agent/
├── core/
│   ├── agent.py      # Main LangGraph agent implementation
│   ├── config.py     # Configuration management
│   └── state.py      # Agent state definition
├── tools/
│   └── basic_tools.py # Basic utility tools
└── cli/
    └── main.py       # CLI interface
```

## Development

To extend the agent with new tools:

1. Add new tool functions to `src/life_agent/tools/`
2. Import and register them in `basic_tools.py`
3. The agent will automatically have access to the new tools

## Requirements

- Python 3.12+
- OpenAI API key
- Dependencies managed by uv