# -*- coding: utf-8 -*-
"""Main CLI interface for the Life Agent."""

import click
import sys
from typing import List, Dict
from ..core.agent import create_agent
from ..core.config import get_config


@click.group()
@click.version_option(version="0.1.0")
def cli():
    """Life Agent - Your AI assistant for life management."""
    pass


@cli.command()
@click.option('--interactive', '-i', is_flag=True, help='Start interactive chat mode')
@click.argument('message', required=False)
def chat(interactive, message):
    """Chat with the Life Agent."""
    try:
        # Validate configuration
        config = get_config()
        agent = create_agent()
        
        if interactive or not message:
            _interactive_chat(agent)
        else:
            # Single message mode
            response = agent.chat(message)
            click.echo(f"\nAgent: {response}\n")
            
    except ValueError as e:
        click.echo(f"Configuration Error: {e}", err=True)
        click.echo("Please check your .env file and ensure OPENAI_API_KEY is set.", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


def _interactive_chat(agent):
    """Run interactive chat mode."""
    click.echo("Life Agent - Interactive Chat Mode")
    click.echo("Type 'quit', 'exit', or 'bye' to end the conversation.\n")
    
    conversation_history: List[Dict[str, str]] = []
    
    while True:
        try:
            user_input = click.prompt("You", type=str)
            
            # Check for exit commands
            if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                click.echo("Goodbye! Have a great day!")
                break
            
            # Get agent response
            response = agent.chat(user_input, conversation_history)
            
            # Display response
            click.echo(f"\nAgent: {response}\n")
            
            # Update conversation history
            conversation_history.append({"role": "user", "content": user_input})
            conversation_history.append({"role": "assistant", "content": response})
            
            # Keep conversation history manageable (last 10 exchanges)
            if len(conversation_history) > 20:
                conversation_history = conversation_history[-20:]
                
        except KeyboardInterrupt:
            click.echo("\n\nGoodbye! Have a great day!")
            break
        except Exception as e:
            click.echo(f"\nError: {e}\n", err=True)


@cli.command()
def config():
    """Show current configuration."""
    try:
        config = get_config()
        click.echo("Current Configuration:")
        click.echo(f"  Model: {config.model}")
        click.echo(f"  Temperature: {config.temperature}")
        click.echo(f"  Max Tokens: {config.max_tokens}")
        click.echo(f"  OpenAI API Key: {'Set' if config.openai_api_key else 'Not Set'}")
        click.echo(f"  LangSmith Tracing: {'Enabled' if config.langchain_tracing_v2 else 'Disabled'}")
        if config.langchain_tracing_v2:
            click.echo(f"  LangSmith Project: {config.langchain_project}")
    except Exception as e:
        click.echo(f"Error reading configuration: {e}", err=True)


@cli.command()
def tools():
    """List available tools."""
    click.echo("Available Tools:")
    click.echo("  - get_current_time - Get current date and time")
    click.echo("  - calculate - Perform basic mathematical calculations")
    click.echo("  - get_day_of_week - Get day of week for a date")
    click.echo("  - days_between_dates - Calculate days between two dates")


if __name__ == '__main__':
    cli()
