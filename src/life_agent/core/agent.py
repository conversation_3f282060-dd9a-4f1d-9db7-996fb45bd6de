"""Main Life Agent implementation using LangGraph."""

from typing import Dict, Any, List
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import Tool<PERSON>ode

from .config import get_config
from .state import AgentState
from ..tools.basic_tools import get_basic_tools


class LifeAgent:
    """Main Life Agent class using LangGraph."""
    
    def __init__(self):
        """Initialize the Life Agent."""
        self.config = get_config()
        self.llm = ChatOpenAI(
            api_key=self.config.openai_api_key,
            model=self.config.model,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )
        
        # Get available tools
        self.tools = get_basic_tools()
        self.llm_with_tools = self.llm.bind_tools(self.tools)
        
        # Create the graph
        self.graph = self._create_graph()
    
    def _create_graph(self) -> StateGraph:
        """Create the LangGraph workflow."""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("agent", self._agent_node)
        workflow.add_node("tools", ToolNode(self.tools))
        
        # Set entry point
        workflow.set_entry_point("agent")
        
        # Add conditional edges
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "continue": "tools",
                "end": END,
            }
        )
        
        # Add edge from tools back to agent
        workflow.add_edge("tools", "agent")
        
        return workflow.compile()
    
    def _agent_node(self, state: AgentState) -> Dict[str, Any]:
        """Process the agent's reasoning and response."""
        messages = state["messages"]
        
        # Add system message if this is the first interaction
        if not messages or not isinstance(messages[0], SystemMessage):
            system_message = SystemMessage(content=self._get_system_prompt())
            messages = [system_message] + messages
        
        # Get response from LLM
        response = self.llm_with_tools.invoke(messages)
        
        return {
            "messages": messages + [response],
            "agent_response": response.content if hasattr(response, 'content') else str(response)
        }
    
    def _should_continue(self, state: AgentState) -> str:
        """Determine if the agent should continue or end."""
        messages = state["messages"]
        last_message = messages[-1]
        
        # If the last message has tool calls, continue to tools
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        
        return "end"
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the agent."""
        return """You are a helpful Life Agent assistant designed to help users manage various aspects of their life.

You have access to various tools to help users with:
- Time and date information
- Basic calculations
- General assistance and advice

Always be helpful, friendly, and provide practical advice. When using tools, explain what you're doing and why.
If you don't have the right tool for a task, explain what you would need and suggest alternatives."""
    
    def chat(self, user_input: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """Chat with the agent."""
        # Convert conversation history to messages
        messages = []
        if conversation_history:
            for turn in conversation_history:
                if turn["role"] == "user":
                    messages.append(HumanMessage(content=turn["content"]))
                elif turn["role"] == "assistant":
                    messages.append(AIMessage(content=turn["content"]))
        
        # Add current user input
        messages.append(HumanMessage(content=user_input))
        
        # Create initial state
        initial_state = {
            "messages": messages,
            "user_input": user_input,
            "agent_response": None,
            "context": {},
            "tool_results": [],
            "current_step": "agent",
            "should_continue": True
        }
        
        # Run the graph
        result = self.graph.invoke(initial_state)
        
        # Return the agent's response
        return result.get("agent_response", "I'm sorry, I couldn't process your request.")


def create_agent() -> LifeAgent:
    """Create and return a Life Agent instance."""
    return LifeAgent()
