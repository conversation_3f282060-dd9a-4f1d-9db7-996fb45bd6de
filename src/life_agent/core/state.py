"""State management for the Life Agent."""

from typing import List, Dict, Any, Optional
from typing_extensions import TypedDict
from langchain_core.messages import BaseMessage


class AgentState(TypedDict):
    """State for the Life Agent graph."""
    
    # Conversation history
    messages: List[BaseMessage]
    
    # Current user input
    user_input: str
    
    # Agent's response
    agent_response: Optional[str]
    
    # Context and memory
    context: Dict[str, Any]
    
    # Tool results
    tool_results: List[Dict[str, Any]]
    
    # Current step in the workflow
    current_step: str
    
    # Whether the conversation should continue
    should_continue: bool
