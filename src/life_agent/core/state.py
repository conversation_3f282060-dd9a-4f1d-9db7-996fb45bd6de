# -*- coding: utf-8 -*-
"""State management for the Life Agent."""

from typing import List, Dict, Any, Optional
from typing_extensions import TypedDict
from langchain_core.messages import BaseMessage
from enum import Enum


class AgentPhase(Enum):
    """The four phases of the Agent's main loop."""
    PLAN = "PLAN"
    ACT = "ACT"
    REACT = "REACT"
    REFLECT = "REFLECT"
    IDLE = "IDLE"


class AgentState(TypedDict):
    """State for the Life Agent graph - focused on the Plan-Act-React-Reflect cycle."""

    # Core Agent Loop
    current_phase: str  # AgentPhase enum value
    loop_iteration: int

    # Conversation history (for CLI interactions)
    messages: List[BaseMessage]

    # Current operation context
    current_goal_id: Optional[int]
    current_task_id: Optional[int]
    current_execution_log_id: Optional[int]

    # Human Tool Interface results
    human_responses: Dict[str, Any]

    # Agent's internal reasoning
    agent_reasoning: str
    agent_decision: str

    # Scheduling and timing
    scheduled_tasks: List[Dict[str, Any]]
    next_trigger_time: Optional[str]

    # Reflection data
    analysis_results: Dict[str, Any]
    insights: List[Dict[str, Any]]

    # System state
    should_continue: bool
    error_message: Optional[str]
